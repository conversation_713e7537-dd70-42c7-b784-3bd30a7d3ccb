'use client';
import React, { useEffect, useState } from 'react';
import Swal from 'sweetalert2';
import { Tooltip } from 'react-tooltip';
import { approveRejectVendor, getVendorDetailsAsync } from '@/store/api/vendorsApi';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { useParams, useSearchParams } from 'next/navigation';



const VendorProfileReview = () => {
  const searchParams = useSearchParams();
  const vendorId = searchParams.get('vendorId'); // ✅ get query param

  const dispatch = useDispatch();

  useEffect(() => {
    if (vendorId) {
      setId(vendorId);
      dispatch(getVendorDetailsAsync(vendorId));
    }
  }, [dispatch]);


  const [activeTab, setActiveTab] = useState('seller');
  const [internalNotes, setInternalNotes] = useState('');
  const [id, setId] = useState('')

  const { vendorsDetails,
    vendorsDetailsLoading,
    vendorsDetailsError } = useSelector((state) => state?.vendorManagement);

  // Sample vendor data - in real app, this would come from API
  const vendorData =
    vendorsDetails?.data
    || {};

const tabs = [
  { id: 'seller', label: 'Seller Information', icon: 'icon-user' },
  { id: 'businessInfo', label: 'Business Information', icon: 'icon-phone' },
  { id: 'categories', label: 'Categories Served', icon: 'icon-map-underlined' },
  { id: 'banking', label: 'Banking', icon: 'icon-bank' },
  { id: 'documents', label: 'Documents', icon: 'icon-file-text' },
  { id: 'products', label: 'Products', icon: 'icon-package-lined' },
  { id: 'performance', label: 'Performance', icon: 'icon-trend-up' },
];

const getStatusBadge = (status) => {
  const statusConfig = {
    Pending: 'bg-yellow-100 text-yellow-800',
    Approved: 'bg-green-100 text-green-800',
    Rejected: 'bg-red-100 text-red-800',
    Suspended: 'bg-gray-100 text-gray-800',
  };

  return (
    <span className={`px-3 py-1 rounded-full text-sm font-semibold ${statusConfig[status] || 'bg-gray-100 text-gray-800'}`}>
      {status}
    </span>
  );
};

const getDocumentStatusBadge = (status) => {
  const statusConfig = {
    Pending: 'bg-yellow-100 text-yellow-800',
    Approved: 'bg-green-100 text-green-800',
    Rejected: 'bg-red-100 text-red-800',
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusConfig[status] || 'bg-gray-100 text-gray-800'}`}>
      {status}
    </span>
  );
};

const handleVendorAction = async (action) => {
  // let title, text, confirmButtonText, confirmButtonColor;

  // switch (action) {
  //   case 'approve':
  //     title = 'Approve Vendor';
  //     text = `Are you sure you want to approve ${vendorData?.vendorName}?`;
  //     confirmButtonText = 'Yes, Approve';
  //     confirmButtonColor = '#10B857';
  //     break;
  //   case 'reject':
  //     title = 'Reject Vendor';
  //     text = `Are you sure you want to reject ${vendorData?.vendorName}?`;
  //     confirmButtonText = 'Yes, Reject';
  //     confirmButtonColor = '#EF4444';
  //     break;
  //   case 'suspend':
  //     title = 'Suspend Vendor';
  //     text = `Are you sure you want to suspend ${vendorData?.vendorName}?`;
  //     confirmButtonText = 'Yes, Suspend';
  //     confirmButtonColor = '#F59E0B';
  //     break;
  //   case 'activate':
  //     title = 'Activate Vendor';
  //     text = `Are you sure you want to activate ${vendorData?.vendorName}?`;
  //     confirmButtonText = 'Yes, Activate';
  //     confirmButtonColor = '#10B857';
  //     break;
  //   default:
  //     return;
  // }

  // const result = await Swal.fire({
  //   title,
  //   text,
  //   icon: 'question',
  //   showCancelButton: true,
  //   confirmButtonColor,
  //   cancelButtonColor: '#6B7280',
  //   confirmButtonText,
  //   cancelButtonText: 'Cancel',
  //   customClass: {
  //     popup: 'rounded-xl',
  //     confirmButton: 'rounded-lg px-4 py-2',
  //     cancelButton: 'rounded-lg px-4 py-2'
  //   }
  // });

  // if (result.isConfirmed) {
  //   Swal.fire({
  //     title: 'Success!',
  //     text: `Vendor has been ${action}d successfully.`,
  //     icon: 'success',
  //     confirmButtonColor: '#10B857',
  //     customClass: {
  //       popup: 'rounded-xl',
  //       confirmButton: 'rounded-lg px-4 py-2'
  //     }
  //   });
  // }

  // let payload = {

  // }
  dispatch(approveRejectVendor({ id: id, status: 'approved' }))
};

const handleDocumentAction = (docType, action) => {
  console.log(`${action} document: ${docType}`);
  // Implement document action logic
};

const handleAddNote = () => {
  if (internalNotes.trim()) {
    console.log('Adding note:', internalNotes);
    setInternalNotes('');
    // Implement add note logic
  }
};

const InfoRow = ({ label, value, className = '' }) => (
  <div className={`flex justify-between flex-col ${className}`}>
    <span className="text-sm text-gray-300">{label}</span>
    <span className="text-sm font-medium text-dark-500">{value || '-'}</span>
  </div>
);

const BadgeRow = ({ value }) => (
  <div className="relative inline-flex items-center px-3 py-1.5 rounded-full font-medium text-xs cursor-pointer bg-primary-500/5 border border-primary-500/10 text-primary-500">
    {value}
  </div>
);

const renderSellerInfo = () => (
  <div className="space-y-6">
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Primary Contact Information</h3>
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-5">
        <InfoRow label="Primary Contact" value={vendorData?.primary_contact_person} />
        <InfoRow label="Country of citizenship" value={vendorData?.citizenship} />
        <InfoRow label="Mobile Number" value={vendorData?.phone} />
        <InfoRow label="Landline Number" value={vendorData?.landline_number} />
        <InfoRow label="Primary Contact Person" value={vendorData?.is_primary_contact ? 'Yes' : 'No'} />
        <InfoRow label="SSN Number" value={vendorData?.ssn} />
        <InfoRow label="Email Address" value={vendorData?.email} />
        {/* <InfoRow label="WhatsApp Available" value={vendorData?.whatsappAvailable ? 'Yes' : 'No'} /> */}
      </div>
    </div>
    <hr />
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Residential Address</h3>
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-5">
        <InfoRow label="Address" value={vendorData?.address_line1} />
        <InfoRow label="City / Town" value={vendorData?.city} />
        <InfoRow label="State / Region" value={vendorData?.state} />
        <InfoRow label="ZIP / Postal Code" value={vendorData?.zipcode} />
        {/* <InfoRow label="Country" value={vendorData?.country} /> */}
      </div>
    </div>
  </div>
);

const renderBusinessInfo = () => (
  <div className="space-y-6">
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Business Details</h3>
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-5">
        <InfoRow label="Vendor Name" value={vendorData?.business_name} />
        <InfoRow label="Business Type" value={vendorData?.business_type} />
        <InfoRow label="Year of Establishment" value={vendorData?.year_of_establishment} />
        <InfoRow label="Number of Employees" value={vendorData?.number_of_employees} />
        <InfoRow label="Primary Location" value={vendorData?.primary_location} />
        <InfoRow label="Annual Revenue" value={vendorData?.annual_revenue} />
        <InfoRow label="Business Phone Number" value={vendorData?.business_phone} />
        <InfoRow label="Business Website URL" value={vendorData?.business_website_url} />
        <InfoRow label="Tax ID (EIN/SSN)" value={vendorData?.gstTaxId} />
        <InfoRow label="Business License Number" value={vendorData?.business_registration_country} />
        <InfoRow label="Created Date" value={new Date(vendorData?.createdDate).toLocaleDateString()} />
        <InfoRow label="Last Updated" value={new Date(vendorData?.lastUpdated).toLocaleDateString()} />
      </div>
    </div>
    <hr />
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Operating Industry</h3>
      <div className="flex flex-wrap gap-2">
        {vendorData?.operatingIndustry?.map((industry, index) => <BadgeRow key={index} value={industry} />)}
      </div>

      <div className="mt-4">
        <h4 className="text-sm font-medium mb-2">Hotel Brand  and Franchise</h4>
        {vendorData?.hotelBrandAndFranchise?.map((item, index) => (
          <div key={index} className="flex items-center py-1.5 border-b border-border-color pb-2">
            <span className="text-sm text-gray-400 font-normal flex-[0_0_300px]">
              {item.name}
            </span>
            {item.franchises.map((value, index) => (
              <div
                key={index}
                className="inline-flex items-center text-sm font-medium"
              >
                {value}
                {index !== item.franchises.length - 1 && (
                  <span className="text-gray-400 mr-1">, </span>
                )}
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  </div>
);

const renderCategories = () => (
  <div className="space-y-6">
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Product categories</h3>
      <div className="flex flex-wrap gap-2">
        {vendorData?.productCategories?.map((category, index) => <BadgeRow key={index} value={category} />)}
      </div>
    </div>
    <hr />
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Service categories</h3>
      <div className="flex flex-wrap gap-2">
        {vendorData?.serviceCategories?.map((category, index) => <BadgeRow key={index} value={category} />)}
      </div>
    </div>
  </div>
);

const renderBankingInfo = () => (
  <div className="space-y-6">
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Banking & Payment Details</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
        <InfoRow label="Bank Account Name" value={vendorData?.bankAccountName} />
        <InfoRow label="Bank Account Number" value={vendorData?.bankAccountNumber} />
        <InfoRow label="IFSC/SWIFT Code" value={vendorData?.ifscCode} />
        <InfoRow label="Bank Name & Branch" value={`${vendorData?.bankName} - ${vendorData?.bankBranch}`} />
        <InfoRow label="UPI ID" value={vendorData?.upiId} />
        <InfoRow label="PAN/Tax Number" value={vendorData?.panNumber} />
      </div>
    </div>
  </div>
);

const renderDocuments = () => (
  <div className="space-y-6">
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Business Documents</h3>
      <div className="space-y-4">
        {vendorData?.documents?.map((doc, index) => (
          <div key={index} className="flex items-center justify-between px-3 py-2 border border-border-color rounded-lg">
            <div className="flex items-center space-x-4">
              <span className="w-8 h-8 flex items-center justify-center bg-gray-500/10 rounded-lg icon icon-file-text text-gray-500 text-xl" />
              <div>
                <h4 className="font-medium text-gray-900 text-sm">{doc.type}</h4>
                <p className="text-sm text-gray-300">Uploaded: {new Date(doc.uploadDate).toLocaleDateString()}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {getDocumentStatusBadge(doc.status)}
              <div className="flex gap-x-2">
                <button
                  onClick={() => window.open(doc.url, '_blank')}
                  data-tooltip-id="download-tooltip"
                  data-tooltip-content="Download"
                  className="flex justify-center items-center h-7 w-7 text-lg bg-dark-500/10 rounded-lg cursor-pointer transition-base"
                >
                  <span className="icon icon-download-simple text-base" />
                </button>
                {doc?.status === 'Pending' && (
                  <>
                    <button
                      onClick={() => handleDocumentAction(doc.type, 'approve')}
                      data-tooltip-id="approve-tooltip"
                      data-tooltip-content="Approve"
                      className="flex justify-center items-center h-7 w-7 text-lg bg-green-500/10 text-green-500 rounded-lg cursor-pointer transition-base"
                    >
                      <span className="icon icon-check-3 " />
                    </button>
                    <button
                      onClick={() => handleDocumentAction(doc.type, 'reject')}
                      data-tooltip-id="reject-tooltip"
                      data-tooltip-content="Reject"
                      className="flex justify-center items-center h-7 w-7 text-lg bg-red-500/10 text-red-500 rounded-lg cursor-pointer transition-base"
                    >
                      <span className="icon icon-x text-base" />
                    </button>
                  </>
                )}

                <Tooltip id="download-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
                <Tooltip id="approve-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
                <Tooltip id="reject-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </div>
);

const renderProducts = () => (
  <div className="space-y-6">
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Product Overview</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div className="text-center p-4 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{vendorData?.totalProducts}</div>
          <div className="text-sm text-gray-400">Total Products</div>
        </div>
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{vendorData?.productApprovalStatus?.approved}</div>
          <div className="text-sm text-gray-400">Approved</div>
        </div>
        <div className="text-center p-4 bg-yellow-50 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600">{vendorData?.productApprovalStatus?.pending}</div>
          <div className="text-sm text-gray-400">Pending</div>
        </div>
      </div>

      <h4 className="font-semibold mb-3">Recent Product Submissions</h4>
      <div className="space-y-3">
        {vendorData?.recentProducts?.map((product, index) => (
          <div key={index} className="flex items-center justify-between p-3 border border-border-color rounded-lg">
            <div>
              <h5 className="font-medium">{product.name}</h5>
              <p className="text-sm text-gray-400">Product ID: {product.id} • {new Date(product.date).toLocaleDateString()}</p>
            </div>
            <div className="flex items-center gap-x-3">
              {getDocumentStatusBadge(product.status)}
              <button
                data-tooltip-id="view-tooltip"
                data-tooltip-content="View details"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg bg-dark-500/10 rounded-lg cursor-pointer transition-base"
              >
                <span className="icon icon-eye text-base" />
              </button>
              <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-100">
        <button className="btn btn-outline-primary">
          <span className="icon icon-package mr-2" />
          View All Products
        </button>
      </div>
    </div>
  </div>
);

const renderPerformance = () => (
  <div className="space-y-6">
    <div className="bg-white rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Performance Overview</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="text-center p-4 bg-yellow-50 rounded-lg">
          <div className="flex items-center justify-center mb-2">
            <span className="icon icon-star text-yellow-400 text-xl mr-1" />
            <span className="text-2xl font-bold text-yellow-600">{vendorData?.averageRating}</span>
          </div>
          <div className="text-sm text-gray-400">Average Rating</div>
        </div>
        <div className="text-center p-4 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600">{vendorData?.returnRefundRatio}</div>
          <div className="text-sm text-gray-400">Return/Refund Ratio</div>
        </div>
        <div className="text-center p-4 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{vendorData?.ordersFulfilled}</div>
          <div className="text-sm text-gray-400">Orders Fulfilled</div>
        </div>
        <div className="text-center p-4 bg-orange-50 rounded-lg">
          <div className="text-2xl font-bold text-orange-600">{vendorData?.complaintsReceived}</div>
          <div className="text-sm text-gray-400">Complaints Received</div>
        </div>
      </div>
    </div>
  </div>
);

return (
  <div className="space-y-6">
    {/* Header with Vendor Info and Actions */}
    <div className="card">
      <div className="flex justify-between items-center">
        <div>
          <div className='inline-flex gap-2 items-center'>
            <h2 className="text-2xl font-bold text-gray-900">{vendorData?.vendorName}</h2>
            {getStatusBadge(vendorData?.status)}
          </div>
          <p className="text-gray-300 mt-1 font-medium text-sm">Vendor ID: #{vendorData?.vendor_code}</p>
        </div>
        <div className="flex gap-2">
          {vendorData?.status === 'Pending' && (
            <>
              <button
                onClick={() => handleVendorAction('approved')}
                className="btn btn-outline-success inline-flex items-center text-white"
              >
                <span className="icon icon-check-3 mr-2 font-bold !font-base" />
                Approve
              </button>
              <button
                onClick={() => handleVendorAction('rejected')}
                className="btn btn-outline-danger text-white"
              >
                <span className="icon icon-x mr-2" />
                Reject
              </button>
            </>
          )}
          {vendorData?.status === 'Approved' && (
            <button
              onClick={() => handleVendorAction('suspend')}
              className="btn bg-yellow-500 hover:bg-yellow-600 text-white"
            >
              <span className="icon icon-pause mr-2" />
              Suspend
            </button>
          )}
          {vendorData?.status === 'Suspended' && (
            <button
              onClick={() => handleVendorAction('activate')}
              className="btn bg-green-500 hover:bg-green-600 text-white"
            >
              <span className="icon icon-play mr-2" />
              Activate
            </button>
          )}
        </div>
      </div>
    </div>

    {/* Tab Navigation */}
    <div className="card !p-0 rounded-lg">
      <div className="border-b border-border-color">
        <nav className="flex space-x-8 px-6">
          {tabs?.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`inline-flex items-center py-4 px-1 border-b-2 font-semibold text-sm transition-colors ${activeTab === tab.id
                ? 'border-primary-500 text-primary-500'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
            >
              {/* <span className={`icon ${tab.icon} mr-2`} /> */}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'seller' && renderSellerInfo()}
        {activeTab === 'businessInfo' && renderBusinessInfo()}
        {activeTab === 'categories' && renderCategories()}
        {activeTab === 'banking' && renderBankingInfo()}
        {activeTab === 'documents' && renderDocuments()}
        {activeTab === 'products' && renderProducts()}
        {activeTab === 'performance' && renderPerformance()}
      </div>
    </div>

    {/* Internal Notes Section */}
    <div className="card">
      <h3 className="text-lg font-semibold mb-4">Internal Notes</h3>
      <div className="space-y-4">
        <div className="flex flex-col space-y-4">
          <textarea
            value={internalNotes}
            onChange={(e) => setInternalNotes(e.target.value)}
            placeholder="Add internal notes about this vendor..."
            className="flex-1 p-3 border border-border-color rounded-lg resize-none focus:ring-1 focus:ring-dark-500 focus:border-transparent outline-0"
            rows="3"
          />
          <button
            onClick={handleAddNote}
            disabled={!internalNotes.trim()}
            className="btn btn-primary px-6 self-end disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Add Note
          </button>
        </div>

          {/* Sample existing notes */}
          <div className="space-y-3 mt-6">
            <h4 className="font-semibold mb-3">Existing Notes</h4>
            <div className="px-4 py-2 bg-gray-50 border border-border-color rounded-lg">
              <div className="flex justify-between items-start mb-1">
                <span className="font-medium text-sm text-gray-900">Admin User</span>
                <span className="text-xs text-gray-400">2024-03-08 10:30 AM</span>
              </div>
              <p className="text-gray-700 text-sm">Vendor documentation looks complete. Banking details verified.</p>
            </div>
            <div className="px-4 py-2 bg-gray-50 border border-border-color rounded-lg">
              <div className="flex justify-between items-start mb-1">
                <span className="font-medium text-sm text-gray-900">Review Team</span>
                <span className="text-xs text-gray-400">2024-03-07 2:15 PM</span>
              </div>
              <p className="text-gray-700 text-sm">Initial review completed. Waiting for GST certificate verification.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Vendor Actions */}
      <div className="card">
        <h3 className="text-lg font-semibold mb-4">Contact Vendor</h3>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => window.open(`mailto:${vendorData.email}`, '_blank')}
            className="btn btn-outline-gray inline-flex items-center"
          >
            <span className="icon icon-message text-base mr-2" />
            Send Email
          </button>
          <button
            onClick={() => window.open(`tel:${vendorData.mobile}`, '_blank')}
            className="btn btn-outline-gray inline-flex items-center"
          >
            <span className="icon icon-phone text-base mr-2" />
            Call Vendor
          </button>
          <button className="btn btn-outline-gray inline-flex items-center">
            <span className="icon icon-chat-circle-dots text-base mr-2" />
            Internal Chat
          </button>
        </div>
      </div>
    </div>
  );
};

export default VendorProfileReview;
