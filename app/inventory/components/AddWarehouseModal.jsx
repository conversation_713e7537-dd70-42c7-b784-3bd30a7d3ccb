'use client';

import React, { useState, useEffect } from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const AddWarehouseModal = ({ isOpen, onClose, onAddWarehouse, warehouse }) => {
  const [formData, setFormData] = useState({
    warehouseName: '',
    warehouseCode: '',
    location: '',
    addressLine: '',
    contactPerson: '',
    contactNumber: '',
    email: '',
    warehouseType: 'Owned',
    capacity: '',
    status: 'Active'
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditMode = !!warehouse;

  useEffect(() => {
    if (isOpen) {
      if (warehouse) {
        // Edit mode - populate form with warehouse data
        setFormData({
          warehouseName: warehouse.warehouseName || '',
          warehouseCode: warehouse.warehouseCode || '',
          location: warehouse.location || '',
          addressLine: warehouse.addressLine || '',
          contactPerson: warehouse.contactPerson || '',
          contactNumber: warehouse.contactNumber || '',
          email: warehouse.email || '',
          warehouseType: warehouse.warehouseType || 'Owned',
          capacity: warehouse.capacity || '',
          status: warehouse.status || 'Active'
        });
      } else {
        // Add mode - reset form
        setFormData({
          warehouseName: '',
          warehouseCode: '',
          location: '',
          addressLine: '',
          contactPerson: '',
          contactNumber: '',
          email: '',
          warehouseType: 'Owned',
          capacity: '',
          status: 'Active'
        });
      }
      setErrors({});
    }
  }, [isOpen, warehouse]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.warehouseName.trim()) {
      newErrors.warehouseName = 'Warehouse name is required';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.capacity && (isNaN(formData.capacity) || formData.capacity < 0)) {
      newErrors.capacity = 'Please enter a valid capacity';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const warehouseData = {
        ...formData,
        capacity: formData.capacity ? parseInt(formData.capacity) : null
      };

      if (isEditMode) {
        // Update existing warehouse
        warehouseData.id = warehouse.id;
        warehouseData.createdAt = warehouse.createdAt;
        warehouseData.updatedAt = new Date().toISOString();
      }

      onAddWarehouse(warehouseData);
      onClose();
    } catch (error) {
      console.error('Error saving warehouse:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const warehouseTypes = [
    { value: 'Owned', label: 'Owned' },
    { value: '3PL', label: '3PL' },
    { value: 'Vendor-managed', label: 'Vendor-managed' }
  ];

  const statusOptions = [
    { value: 'Active', label: 'Active' },
    { value: 'Inactive', label: 'Inactive' }
  ];

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={isEditMode ? 'Edit Warehouse' : 'Add New Warehouse'}
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-6 p-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Warehouse Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="warehouseName"
                value={formData.warehouseName}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.warehouseName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter warehouse name"
              />
              {errors.warehouseName && (
                <p className="text-red-500 text-xs mt-1">{errors.warehouseName}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Warehouse Code
              </label>
              <input
                type="text"
                name="warehouseCode"
                value={formData.warehouseCode}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter warehouse code"
              />
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Location Information
            </h4>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Location (City, State) <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.location ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="e.g., Los Angeles, CA"
              />
              {errors.location && (
                <p className="text-red-500 text-xs mt-1">{errors.location}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Address
              </label>
              <textarea
                name="addressLine"
                value={formData.addressLine}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter full address"
              />
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Contact Information
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Person
                </label>
                <input
                  type="text"
                  name="contactPerson"
                  value={formData.contactPerson}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter contact person name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Number
                </label>
                <input
                  type="tel"
                  name="contactNumber"
                  value={formData.contactNumber}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter phone number"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter email address"
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>
          </div>

          {/* Warehouse Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Warehouse Type
              </label>
              <select
                name="warehouseType"
                value={formData.warehouseType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                {warehouseTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Capacity (sq.ft)
              </label>
              <input
                type="number"
                name="capacity"
                value={formData.capacity}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.capacity ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter capacity"
                min="0"
              />
              {errors.capacity && (
                <p className="text-red-500 text-xs mt-1">{errors.capacity}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                {statusOptions.map(status => (
                  <option key={status.value} value={status.value}>
                    {status.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : (isEditMode ? 'Update Warehouse' : 'Add Warehouse')}
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default AddWarehouseModal;
