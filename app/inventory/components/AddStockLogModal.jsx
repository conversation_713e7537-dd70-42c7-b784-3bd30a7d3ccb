'use client';

import React, { useState, useEffect } from 'react';
// import BaseOffCanvas from '../../components/modals/BaseOffCanvas';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const AddStockLogModal = ({ isOpen, onClose, onAddLog }) => {
  const [formData, setFormData] = useState({
    productSku: '',
    productName: '',
    variant: '',
    warehouseId: '',
    stockInOut: 'In',
    quantity: '',
    reason: '',
    referenceType: 'Manual',
    referenceId: '',
    remarks: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample data - in real app this would come from API
  const products = [
    { sku: 'WBH-001', name: 'Wireless Bluetooth Headphones', variants: ['Black - Standard', 'White - Standard'] },
    { sku: 'CTS-002', name: 'Cotton T-Shirt', variants: ['Red - Small', 'Red - Medium', 'Red - Large', 'Blue - Small', 'Blue - Medium'] },
    { sku: 'OCB-003', name: 'Office Chair Bundle', variants: [] },
    { sku: 'SPC-004', name: 'Smartphone Case', variants: ['iPhone 14 - Clear', 'iPhone 14 - Black', 'iPhone 15 - Clear'] },
    { sku: 'GL-005', name: 'Gaming Laptop', variants: [] }
  ];

  const warehouses = [
    { id: 'WH-001', name: 'Main Distribution Center' },
    { id: 'WH-002', name: 'East Coast Fulfillment' },
    { id: 'WH-003', name: 'Vendor Storage Facility' },
    { id: 'WH-004', name: 'Seasonal Storage Hub' }
  ];

  const reasonOptions = {
    In: ['Purchase Inward', 'Return Processing', 'Stock Transfer', 'Found Stock', 'Manual Adjustment', 'Other'],
    Out: ['Order Fulfillment', 'Damage Out', 'Stock Transfer', 'Shrinkage', 'Manual Adjustment', 'Other']
  };

  const referenceTypes = ['Manual', 'Purchase Order', 'Sales Order', 'Return', 'Transfer', 'Adjustment'];

  useEffect(() => {
    if (isOpen) {
      setFormData({
        productSku: '',
        productName: '',
        variant: '',
        warehouseId: '',
        stockInOut: 'In',
        quantity: '',
        reason: '',
        referenceType: 'Manual',
        referenceId: '',
        remarks: ''
      });
      setErrors({});
    }
  }, [isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Handle product selection
    if (name === 'productSku') {
      const selectedProduct = products.find(p => p.sku === value);
      setFormData(prev => ({
        ...prev,
        productName: selectedProduct ? selectedProduct.name : '',
        variant: ''
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.productSku) {
      newErrors.productSku = 'Please select a product';
    }

    if (!formData.warehouseId) {
      newErrors.warehouseId = 'Please select a warehouse';
    }

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Please enter a valid quantity';
    }

    if (!formData.reason) {
      newErrors.reason = 'Please select a reason';
    }

    if (formData.referenceType !== 'Manual' && !formData.referenceId.trim()) {
      newErrors.referenceId = 'Reference ID is required for this reference type';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const selectedProduct = products.find(p => p.sku === formData.productSku);
      const selectedWarehouse = warehouses.find(w => w.id === formData.warehouseId);

      const logData = {
        productName: selectedProduct.name,
        sku: formData.productSku,
        variant: formData.variant || null,
        warehouseName: selectedWarehouse.name,
        stockInOut: formData.stockInOut,
        quantity: parseInt(formData.quantity),
        reason: formData.reason,
        referenceType: formData.referenceType,
        referenceId: formData.referenceId || `MAN-${Date.now()}`,
        remarks: formData.remarks,
        thumbnail: '/images/product-1.jpg' // Default thumbnail
      };

      onAddLog(logData);
      onClose();
    } catch (error) {
      console.error('Error adding stock log:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedProduct = products.find(p => p.sku === formData.productSku);
  const availableReasons = reasonOptions[formData.stockInOut] || [];

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Add Stock Log"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-6 p-6">
          {/* Product Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product <span className="text-red-500">*</span>
              </label>
              <select
                name="productSku"
                value={formData.productSku}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.productSku ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select a product</option>
                {products.map(product => (
                  <option key={product.sku} value={product.sku}>
                    {product.sku} - {product.name}
                  </option>
                ))}
              </select>
              {errors.productSku && (
                <p className="text-red-500 text-xs mt-1">{errors.productSku}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Variant
              </label>
              <select
                name="variant"
                value={formData.variant}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                disabled={!selectedProduct || selectedProduct.variants.length === 0}
              >
                <option value="">Select variant (if applicable)</option>
                {selectedProduct?.variants.map(variant => (
                  <option key={variant} value={variant}>
                    {variant}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Warehouse and Transaction Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Warehouse <span className="text-red-500">*</span>
              </label>
              <select
                name="warehouseId"
                value={formData.warehouseId}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.warehouseId ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select warehouse</option>
                {warehouses.map(warehouse => (
                  <option key={warehouse.id} value={warehouse.id}>
                    {warehouse.name}
                  </option>
                ))}
              </select>
              {errors.warehouseId && (
                <p className="text-red-500 text-xs mt-1">{errors.warehouseId}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transaction Type <span className="text-red-500">*</span>
              </label>
              <div className="grid grid-cols-2 gap-2">
                <label className="flex items-center p-2 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="stockInOut"
                    value="In"
                    checked={formData.stockInOut === 'In'}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <span className="text-green-600 font-medium">Stock In</span>
                </label>
                <label className="flex items-center p-2 border rounded-lg cursor-pointer hover:bg-gray-50">
                  <input
                    type="radio"
                    name="stockInOut"
                    value="Out"
                    checked={formData.stockInOut === 'Out'}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <span className="text-red-600 font-medium">Stock Out</span>
                </label>
              </div>
            </div>
          </div>

          {/* Quantity and Reason */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantity <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="quantity"
                value={formData.quantity}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.quantity ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter quantity"
                min="1"
              />
              {errors.quantity && (
                <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason <span className="text-red-500">*</span>
              </label>
              <select
                name="reason"
                value={formData.reason}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.reason ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select reason</option>
                {availableReasons.map(reason => (
                  <option key={reason} value={reason}>
                    {reason}
                  </option>
                ))}
              </select>
              {errors.reason && (
                <p className="text-red-500 text-xs mt-1">{errors.reason}</p>
              )}
            </div>
          </div>

          {/* Reference Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reference Type
              </label>
              <select
                name="referenceType"
                value={formData.referenceType}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                {referenceTypes.map(type => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reference ID {formData.referenceType !== 'Manual' && <span className="text-red-500">*</span>}
              </label>
              <input
                type="text"
                name="referenceId"
                value={formData.referenceId}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.referenceId ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder={formData.referenceType === 'Manual' ? 'Auto-generated' : 'Enter reference ID'}
                disabled={formData.referenceType === 'Manual'}
              />
              {errors.referenceId && (
                <p className="text-red-500 text-xs mt-1">{errors.referenceId}</p>
              )}
            </div>
          </div>

          {/* Remarks */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              name="remarks"
              value={formData.remarks}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Additional notes or comments..."
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Adding Log...' : 'Add Stock Log'}
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default AddStockLogModal;
