'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import Swal from 'sweetalert2';
import FilterField from '../../components/table/FilterField';
import WarehouseDetailModal from './WarehouseDetailModal';
import AddWarehouseModal from './AddWarehouseModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const WarehouseDirectoryTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [isWarehouseDetailOpen, setIsWarehouseDetailOpen] = useState(false);
  const [isAddWarehouseOpen, setIsAddWarehouseOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);

  // Sample data for warehouses
  const [warehouses, setWarehouses] = useState([
    {
      id: 'WH-001',
      warehouseName: 'Main Distribution Center',
      warehouseCode: 'MDC-001',
      location: 'Los Angeles, CA',
      addressLine: '1234 Industrial Blvd, Los Angeles, CA 90001',
      contactPerson: 'John Smith',
      contactNumber: '+1-555-0123',
      email: '<EMAIL>',
      warehouseType: 'Owned',
      capacity: 50000,
      status: 'Active',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:20:00Z'
    },
    {
      id: 'WH-002',
      warehouseName: 'East Coast Fulfillment',
      warehouseCode: 'ECF-002',
      location: 'New York, NY',
      addressLine: '5678 Commerce Ave, New York, NY 10001',
      contactPerson: 'Sarah Johnson',
      contactNumber: '+1-555-0456',
      email: '<EMAIL>',
      warehouseType: '3PL',
      capacity: 30000,
      status: 'Active',
      createdAt: '2024-01-10T09:15:00Z',
      updatedAt: '2024-01-18T16:45:00Z'
    },
    {
      id: 'WH-003',
      warehouseName: 'Vendor Storage Facility',
      warehouseCode: 'VSF-003',
      location: 'Chicago, IL',
      addressLine: '9012 Storage St, Chicago, IL 60601',
      contactPerson: 'Mike Wilson',
      contactNumber: '+1-555-0789',
      email: '<EMAIL>',
      warehouseType: 'Vendor-managed',
      capacity: 25000,
      status: 'Active',
      createdAt: '2024-01-12T11:00:00Z',
      updatedAt: '2024-01-19T13:30:00Z'
    },
    {
      id: 'WH-004',
      warehouseName: 'Seasonal Storage Hub',
      warehouseCode: 'SSH-004',
      location: 'Phoenix, AZ',
      addressLine: '3456 Warehouse Way, Phoenix, AZ 85001',
      contactPerson: 'Lisa Brown',
      contactNumber: '+1-555-0321',
      email: '<EMAIL>',
      warehouseType: 'Owned',
      capacity: 15000,
      status: 'Inactive',
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-21T10:15:00Z'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Warehouses', count: warehouses.length },
    { id: 'Active', label: 'Active', count: warehouses.filter(w => w.status === 'Active').length },
    { id: 'Inactive', label: 'Inactive', count: warehouses.filter(w => w.status === 'Inactive').length }
  ];

  const filteredWarehouses = warehouses.filter(warehouse => {
    const matchesSearch = warehouse.warehouseName.toLowerCase().includes(filterText.toLowerCase()) ||
                         warehouse.warehouseCode.toLowerCase().includes(filterText.toLowerCase()) ||
                         warehouse.location.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || warehouse.status === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  const columns = [
    {
      name: 'Warehouse',
      selector: row => row.warehouseName,
      sortable: true,
      cell: (row) => (
        <div className="py-2">
          <div className="font-medium text-sm">{row.warehouseName}</div>
          <div className="text-xs text-gray-300">{row.warehouseCode}</div>
        </div>
      ),
      width: '250px',
    },
    {
      name: 'Location',
      selector: row => row.location,
      sortable: true,
      cell: (row) => (
        <div className="py-2">
          <div className="text-sm">{row.location}</div>
          {row.addressLine && (
            <div className="text-xs text-gray-300 truncate max-w-[200px]" title={row.addressLine}>
              {row.addressLine}
            </div>
          )}
        </div>
      ),
      width: '220px',
    },
    {
      name: 'Contact',
      selector: row => row.contactPerson,
      sortable: true,
      cell: (row) => (
        <div className="py-2">
          <div className="text-sm">{row.contactPerson || '-'}</div>
          <div className="text-xs text-gray-300">{row.contactNumber || '-'}</div>
        </div>
      ),
      width: '180px',
    },
    {
      name: 'Type',
      selector: row => row.warehouseType,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
          row.warehouseType === 'Owned' ? 'bg-green-100 text-green-800' :
          row.warehouseType === '3PL' ? 'bg-info-500/20 text-info-500' :
          'bg-orange-100 text-orange-800'
        }`}>
          {row.warehouseType}
        </span>
      ),
      // width: '140px',
    },
    {
      name: 'Capacity',
      selector: row => row.capacity,
      sortable: true,
      cell: (row) => (
        <span className="text-sm">
          {row.capacity ? `${row.capacity.toLocaleString()} sq.ft` : '-'}
        </span>
      ),
      // width: '120px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          row.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          {row.status}
        </span>
      ),
      // width: '100px',
    },
    {
      name: 'Actions',
      // grow: 0,
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewWarehouse(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <button
            onClick={() => handleEditWarehouse(row)}
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit Warehouse"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-pencil-line text-base" />
          </button>
          <button
            onClick={() => handleDeleteWarehouse(row.id)}
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete Warehouse"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-trash text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // width: '120px',
    },
  ];

  const handleViewWarehouse = (warehouse) => {
    setSelectedWarehouse(warehouse);
    setIsWarehouseDetailOpen(true);
  };

  const handleEditWarehouse = (warehouse) => {
    setSelectedWarehouse(warehouse);
    setIsAddWarehouseOpen(true);
  };

  const handleDeleteWarehouse = async (warehouseId) => {
    const warehouse = warehouses.find(w => w.id === warehouseId);
    
    const result = await Swal.fire({
      title: 'Delete Warehouse',
      html: `
        <div class="text-left">
          <p class="mb-3 text-sm text-center text-gray-400">Are you sure you want to delete this warehouse? This action cannot be undone.</p>
          <div class="bg-gray-50 p-3 rounded-lg border">
            <div class="mb-2">
              <p class="text-sm font-medium text-gray-900">${warehouse?.warehouseName || 'Unknown Warehouse'}</p>
              <p class="text-xs text-gray-500">${warehouse?.warehouseCode || 'N/A'}</p>
            </div>
            <div class="text-xs text-gray-500">
              <p>Location: ${warehouse?.location || 'N/A'}</p>
              <p>Type: ${warehouse?.warehouseType || 'N/A'}</p>
              <p>Status: ${warehouse?.status || 'N/A'}</p>
            </div>
          </div>
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc2626',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Delete Warehouse',
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2 !font-medium',
        cancelButton: '!rounded-lg px-4 py-2 !font-medium'
      }
    });

    if (result.isConfirmed) {
      setWarehouses(prev => prev.filter(w => w.id !== warehouseId));
      
      // Show success message
      Swal.fire({
        title: 'Deleted!',
        text: 'Warehouse has been deleted successfully.',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        customClass: {
          popup: 'rounded-2xl'
        }
      });
      
      console.log('Warehouse deleted:', warehouseId);
    }
  };

  const handleAddWarehouse = (warehouseData) => {
    const newWarehouse = {
      ...warehouseData,
      id: `WH-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setWarehouses(prev => [...prev, newWarehouse]);
    console.log('Warehouse added:', newWarehouse);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <div className="space-y-4 relative">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Warehouse Directory</h2>
          <span className="text-sm text-gray-300">
            {filteredWarehouses.length} of {warehouses.length} warehouses
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-download-simple font-normal text-lg" />
            Import
          </button>
          <button
            onClick={() => {
              setSelectedWarehouse(null);
              setIsAddWarehouseOpen(true);
            }}
            className="btn btn-primary inline-flex items-center gap-2"
          >
            <span className="icon icon-plus font-normal text-lg" />
            Add Warehouse
          </button>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search"
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        />

        <DataTable
          columns={columns}
          data={filteredWarehouses}
          pagination
          paginationPerPage={10}
          selectableRows
          fixedHeader={true}
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-buildings text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No warehouses found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={true}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <WarehouseDetailModal
        isOpen={isWarehouseDetailOpen}
        onClose={() => setIsWarehouseDetailOpen(false)}
        warehouse={selectedWarehouse}
      />

      <AddWarehouseModal
        isOpen={isAddWarehouseOpen}
        onClose={() => {
          setIsAddWarehouseOpen(false);
          setSelectedWarehouse(null);
        }}
        onAddWarehouse={handleAddWarehouse}
        warehouse={selectedWarehouse}
      />
    </div>
  );
};

export default WarehouseDirectoryTab;
