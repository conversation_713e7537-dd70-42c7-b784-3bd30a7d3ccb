'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { createPortal } from 'react-dom';
import DataTable from 'react-data-table-component';
import SortableItem from '../../components/table/SortableItem';
import { Tooltip } from 'react-tooltip';
import Swal from 'sweetalert2';
import FilterField from '../../components/table/FilterField';
import ProductDetailModal from './ProductDetailModal';
import AddProductModal from './AddProductModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Variant Dropdown Component
const VariantDropdown = ({ isVisible, anchorEl, variants, checkKeys, onMouseEnter, onMouseLeave }) => {
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef(null);

  useEffect(() => {
    if (isVisible && anchorEl) {
      const updatePosition = () => {
        const rect = anchorEl.getBoundingClientRect();
        const scrollY = window.scrollY;
        const scrollX = window.scrollX;

        let top = rect.bottom + scrollY;
        let left = rect.left + scrollX;

        if (dropdownRef.current) {
          const dropdownHeight = dropdownRef.current.offsetHeight;
          const viewportHeight = window.innerHeight;
          const bottomSpace = viewportHeight - rect.bottom;

          if (bottomSpace < dropdownHeight) {
            top = rect.top + scrollY - dropdownHeight;
          }
        }

        setPosition({ top, left });
      };

      updatePosition();
      window.addEventListener('scroll', updatePosition, true);
      window.addEventListener('resize', updatePosition);

      return () => {
        window.removeEventListener('scroll', updatePosition, true);
        window.removeEventListener('resize', updatePosition);
      };
    }
  }, [isVisible, anchorEl]);

  if (!isVisible) return null;

  return createPortal(
    <div
      ref={dropdownRef}
      className="fixed z-50"
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
        transform: 'translate3d(0, 0, 0)',
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      <div className="w-[296px] p-3 bg-white rounded-2xl shadow-lg border border-border-color max-h-64 overflow-y-auto">
        {checkKeys?.is_variation && variants?.length > 0 ? (
          variants?.map((variant, index) => (
            <div
              key={index}
              className="flex items-center justify-between px-4 py-2 hover:bg-gray-50 not-last:border-b border-surface-100"
            >
              <span className="text-sm text-dark-500">
                {variant.name || '-'}
              </span>
            </div>
          ))
        ) : (
          <span className="text-sm text-dark-300">No Variants Found</span>
        )}
      </div>
    </div>,
    document.body
  );
};

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';


const ProductsTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isProductDetailOpen, setIsProductDetailOpen] = useState(false);
  const [isAddProductOpen, setIsAddProductOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);

  // Variant dropdown state
  const [isVariantDropdownVisible, setIsVariantDropdownVisible] = useState(false);
  const [variantAnchorEl, setVariantAnchorEl] = useState(null);
  const [currentVariants, setCurrentVariants] = useState([]);
  const [checkKeys, setCheckKeys] = useState({ is_variation: true });
  const hoverTimeoutRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    productName: true,
    Vendor: true,
    productType: true,
    category: true,
    manufacturer: true,
    sellingPrice: true,
    status: true,
    inventory: true,
    variants: true,
  });
  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [products, setProducts] = useState([
    {
      id: 'PRD-001',
      productName: 'Wireless Bluetooth Headphones',
      productCode: 'WBH-001',
      Vendor: 'Tech Sound',
      productType: 'Simple',
      category: { name: 'Electronics', id: 'cat-1' },
      manufacturer: { name: 'TechSound', id: 'manufacturer-1' },
      status: 'Published',
      mrp: 199.99,
      sellingPrice: 149.99,
      costPrice: 89.99,
      tierPricing: 5,
      inventory: 150,
      minOrderQty: 1,
      variants: 0,
      tags: ['wireless', 'bluetooth', 'audio'],
      thumbnail: '/images/product-3.jpg',
      shortDescription: 'High-quality wireless headphones with noise cancellation',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:20:00Z'
    },
    {
      id: 'PRD-002',
      productName: 'Cotton T-Shirt',
      productCode: 'CTS-002',
      Vendor: 'Comfort Wear',
      productType: 'Variant',
      category: { name: 'Clothing', id: 'cat-2' },
      manufacturer: { name: 'ComfortWear', id: 'manufacturer-2' },
      status: 'Published',
      mrp: 29.99,
      sellingPrice: 24.99,
      costPrice: 12.99,
      tierPricing: 5,
      inventory: 500,
      minOrderQty: 1,
      variants: 12,
      variantDetails: [
        {
          name: 'Red - Small',
          sku: 'CTS-002-RED-S',
          price: 24.99,
          stock: 45,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Red' },
            { name: 'Size', value: 'Small' }
          ]
        },
        {
          name: 'Red - Medium',
          sku: 'CTS-002-RED-M',
          price: 24.99,
          stock: 52,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Red' },
            { name: 'Size', value: 'Medium' }
          ]
        },
        {
          name: 'Red - Large',
          sku: 'CTS-002-RED-L',
          price: 24.99,
          stock: 38,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Red' },
            { name: 'Size', value: 'Large' }
          ]
        },
        {
          name: 'Blue - Small',
          sku: 'CTS-002-BLU-S',
          price: 24.99,
          stock: 0,
          status: 'Inactive',
          attributes: [
            { name: 'Color', value: 'Blue' },
            { name: 'Size', value: 'Small' }
          ]
        },
        {
          name: 'Blue - Medium',
          sku: 'CTS-002-BLU-M',
          price: 24.99,
          stock: 28,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Blue' },
            { name: 'Size', value: 'Medium' }
          ]
        },
        {
          name: 'Blue - Large',
          sku: 'CTS-002-BLU-L',
          price: 24.99,
          stock: 15,
          status: 'Active',
          attributes: [
            { name: 'Color', value: 'Blue' },
            { name: 'Size', value: 'Large' }
          ]
        }
      ],
      tags: ['cotton', 'casual', 'comfortable'],
      thumbnail: '/images/product-2.jpg',
      shortDescription: '100% cotton comfortable t-shirt available in multiple colors and sizes',
      createdAt: '2024-01-10T09:15:00Z',
      updatedAt: '2024-01-18T16:45:00Z'
    },
    {
      id: 'PRD-003',
      productName: 'Office Chair Bundle',
      productCode: 'OCB-003',
      Vendor: 'Ergo Desk',
      productType: 'Bundle',
      category: { name: 'Furniture', id: 'cat-3' },
      manufacturer: { name: 'ErgoDesk', id: 'manufacturer-3' },
      status: 'Draft',
      mrp: 399.99,
      sellingPrice: 349.99,
      costPrice: 199.99,
      tierPricing: 5,
      inventory: 25,
      minOrderQty: 1,
      variants: 0,
      tags: ['office', 'ergonomic', 'bundle'],
      thumbnail: '/images/product-1.jpg',
      shortDescription: 'Ergonomic office chair with lumbar support and accessories',
      createdAt: '2024-01-12T11:00:00Z',
      updatedAt: '2024-01-19T13:30:00Z'
    },
    {
      id: 'PRD-004',
      productName: 'Smartphone Case',
      productCode: 'SPC-004',
      Vendor: 'Protect Tech',
      productType: 'Variant',
      category: { name: 'Accessories', id: 'cat-4' },
      manufacturer: { name: 'ProtectTech', id: 'manufacturer-4' },
      status: 'Pending Approval',
      mrp: 19.99,
      sellingPrice: 15.99,
      costPrice: 7.99,
      tierPricing: 5,
      inventory: 300,
      minOrderQty: 1,
      variants: 8,
      variantDetails: [
        {
          name: 'iPhone 14 - Clear',
          sku: 'SPC-004-IP14-CLR',
          price: 15.99,
          stock: 45,
          status: 'Active',
          attributes: [
            { name: 'Model', value: 'iPhone 14' },
            { name: 'Color', value: 'Clear' }
          ]
        },
        {
          name: 'iPhone 14 - Black',
          sku: 'SPC-004-IP14-BLK',
          price: 15.99,
          stock: 38,
          status: 'Active',
          attributes: [
            { name: 'Model', value: 'iPhone 14' },
            { name: 'Color', value: 'Black' }
          ]
        },
        {
          name: 'iPhone 15 - Clear',
          sku: 'SPC-004-IP15-CLR',
          price: 17.99,
          stock: 52,
          status: 'Active',
          attributes: [
            { name: 'Model', value: 'iPhone 15' },
            { name: 'Color', value: 'Clear' }
          ]
        },
        {
          name: 'iPhone 15 - Black',
          sku: 'SPC-004-IP15-BLK',
          price: 17.99,
          stock: 29,
          status: 'Active',
          attributes: [
            { name: 'Model', value: 'iPhone 15' },
            { name: 'Color', value: 'Black' }
          ]
        }
      ],
      tags: ['protection', 'smartphone', 'durable'],
      thumbnail: '/images/product-3.jpg',
      shortDescription: 'Durable smartphone case with drop protection',
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-21T10:15:00Z'
    },
    {
      id: 'PRD-005',
      productName: 'Gaming Laptop',
      productCode: 'GL-005',
      Vendor: 'Game Force',
      productType: 'Simple',
      category: { name: 'Computers', id: 'cat-5' },
      manufacturer: { name: 'GameForce', id: 'manufacturer-5' },
      status: 'Archive',
      mrp: 1299.99,
      sellingPrice: 1199.99,
      costPrice: 899.99,
      tierPricing: 5,
      inventory: 5,
      minOrderQty: 1,
      variants: 0,
      tags: ['gaming', 'laptop', 'high-performance'],
      thumbnail: '/images/product-1.jpg',
      shortDescription: 'High-performance gaming laptop with RTX graphics',
      createdAt: '2024-01-08T16:45:00Z',
      updatedAt: '2024-01-16T12:00:00Z'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Products', count: products.length },
    { id: 'Published', label: 'Published', count: products.filter(p => p.status === 'Published').length },
    { id: 'Draft', label: 'Draft', count: products.filter(p => p.status === 'Draft').length },
    { id: 'Pending Approval', label: 'Pending Approval', count: products.filter(p => p.status === 'Pending Approval').length },
    { id: 'Archive', label: 'Archive', count: products.filter(p => p.status === 'Archive').length }
  ];

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.productName.toLowerCase().includes(filterText.toLowerCase()) ||
                          product.Vendor.toLowerCase().includes(filterText.toLowerCase()) ||
                         product.productCode.toLowerCase().includes(filterText.toLowerCase()) ||
                         product.manufacturer.name.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || product.status === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  // Hover handlers
  const handleVariantHover = (variants, anchorEl) => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    setVariantAnchorEl(anchorEl); // Use the cell div as anchor
    setCurrentVariants(variants);
    setIsVariantDropdownVisible(true);
  };

  const handleVariantLeave = () => {
    hoverTimeoutRef.current = setTimeout(() => {
      setIsVariantDropdownVisible(false);
      setVariantAnchorEl(null);
      setCurrentVariants([]);
    }, 300); // 300ms delay before hiding
  };

  const columns = [
    {
      name: 'Vendor',
      selector: row => row.Vendor,
      sortable: true,
      omit: !displayProperties.Vendor,
      // width: '120px',
    },
    {
      name: 'Product',
      selector: row => row.productName,
      sortable: true,
      omit: !displayProperties.productName,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={row.thumbnail}
              alt={row.productName}
              width={40}
              height={40}
              className="object-cover w-10 h-10 rounded-lg"
            />
          </div>
          <div>
            <div className="font-medium text-sm">{row.productName}</div>
            <div className="text-xs text-gray-500">{row.productCode}</div>
          </div>
        </div>
      ),
      width: '300px',
    },
    
    {
      name: 'Type',
      selector: row => row.productType,
      sortable: true,
      omit: !displayProperties.productType,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
          row.productType === 'Simple' ? 'bg-info-500/10 text-info-500' :
          row.productType === 'Variant' ? 'bg-purple-100 text-purple-800' :
          'bg-orange-100 text-orange-800'
        }`}>
          {row.productType}
        </span>
      ),
      width: '100px',
    },
    {
      name: 'Category',
      selector: row => row.category.name,
      sortable: true,
      omit: !displayProperties.category,
      // width: '120px',
    },
    {
      name: 'Manufacturer',
      selector: row => row.manufacturer.name,
      sortable: true,
      omit: !displayProperties.manufacturer,
      // width: '120px',
    },
    {
      name: 'Price',
      selector: row => row.sellingPrice,
      sortable: true,
      omit: !displayProperties.sellingPrice,
      cell: (row) => (
        <div className="text-sm">
          <div className="font-medium">${row.sellingPrice}</div>
          {row.mrp !== row.sellingPrice && (
            <div className="text-xs text-gray-300 line-through">${row.mrp}</div>
          )}
        </div>
      ),
      // width: '100px',
    },
    // {
    //   name: 'Tier Pricing',
    //   selector: row => row.tierPricing,
    //   sortable: true,
    //   // width: '120px',
    // },
    {
      name: 'Inventory',
      selector: row => row.inventory,
      sortable: true,
      omit: !displayProperties.inventory,
      cell: (row) => (
        <span className={`font-medium ${
          row.inventory < 10 ? 'text-red-600' : 
          row.inventory < 50 ? 'text-yellow-600' : 
          'text-green-600'
        }`}>
          {row.inventory}
        </span>
      ),
      width: '100px',
    },
    {
      name: 'Variants',
      selector: row => row.variants,
      sortable: true,
      omit: !displayProperties.variants,
      cell: (row) => (
        <div
          className="relative"
          onMouseEnter={row.variants > 0 ? (e) => handleVariantHover(row.variantDetails || [], e.currentTarget) : undefined}
          onMouseLeave={row.variants > 0 ? handleVariantLeave : undefined}
        >
          {row.variants > 0 ? (
            <div className="inline-flex items-center gap-1.5 rounded-lg text-[13px] text-nowrap transition-base cursor-pointer">
              <span>{row.variants} variants</span>
              <span className="icon icon-caret-down text-xs" />
            </div>
          ) : (
            <span className="text-gray-400">No variants</span>
          )}
          {/* Variant Dropdown */}
          <VariantDropdown
            isVisible={isVariantDropdownVisible}
            anchorEl={variantAnchorEl}
            variants={currentVariants}
            checkKeys={checkKeys}
            onMouseEnter={() => {
              if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current);
            }}
            onMouseLeave={handleVariantLeave}
          />
        </div>
      ),
      width: '140px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full text-nowrap ${
          row.status === 'Published' ? 'bg-green-100 text-green-800' :
          row.status === 'Draft' ? 'bg-gray-100 text-gray-800' :
          row.status === 'Pending Approval' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {row.status}
        </span>
      ),
      width: '140px',
    },
    {
      name: 'Actions',
      grow: 0,
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewProduct(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <Link
            href={`/manage-products/edit/${row.id}`}
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-pencil-line text-base" />
          </Link>
          <button
            onClick={() => handleCloneProduct(row)}
            data-tooltip-id="clone-tooltip"
            data-tooltip-content="Clone Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-info-500/10 hover:text-info-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-copy-simple text-base" />
          </button>
          <button
            onClick={() => handleDeleteProduct(row.id)}
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-trash text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="clone-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      width: '160px',
    },
  ];

  const handleViewProduct = (product) => {
    setSelectedProduct(product);
    setIsProductDetailOpen(true);
  };

  const handleCloneProduct = (product) => {
    const clonedProduct = {
      ...product,
      id: `PRD-${Date.now()}`,
      productName: `${product.productName} (Copy)`,
      productCode: `${product.productCode}-COPY`,
      status: 'Draft'
    };
    setProducts(prev => [...prev, clonedProduct]);
    console.log('Product cloned:', clonedProduct);
  };

  const handleDeleteProduct = async (productId) => {
    const product = products.find(p => p.id === productId);

    const result = await Swal.fire({
      title: 'Delete Product',
      html: `
        <div class="text-left">
          <p class="mb-3 text-sm text-center text-gray-400">Are you sure you want to delete this product? This action cannot be undone.</p>
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#db3545',
      cancelButtonColor: '#b3b6b5',
      confirmButtonText: 'Yes, Delete Product',
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2 !font-medium',
        cancelButton: '!rounded-lg px-4 py-2 !font-medium'
      }
    });

    if (result.isConfirmed) {
      setProducts(prev => prev.filter(p => p.id !== productId));

      // Show success message
      Swal.fire({
        title: 'Deleted!',
        text: 'Product has been deleted successfully.',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        customClass: {
          popup: 'rounded-2xl'
        }
      });

      console.log('Product deleted:', productId);
    }
  };

  const handleAddProduct = (productData) => {
    const newProduct = {
      ...productData,
      id: `PRD-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setProducts(prev => [...prev, newProduct]);
    console.log('Product added:', newProduct);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <div className="space-y-4 relative">

      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Products</h2>
          <span className="text-sm text-gray-300">
            {filteredProducts.length} of {products.length} products
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-bold text-lg" />
            Export
          </button>
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-download-simple font-bold text-lg" />
            Import
          </button>
          <button
            onClick={() => setIsAddProductOpen(true)}
            className="btn btn-primary inline-flex items-center gap-2"
          >
            <span className="icon icon-plus font-bold text-lg" />
            Add Product
          </button>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search products..."
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        <DataTable
          columns={columns}
          data={filteredProducts}
          pagination
          paginationPerPage={10}
          selectableRows
          fixedHeader={true}
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-package text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No products found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={true}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <ProductDetailModal
        isOpen={isProductDetailOpen}
        onClose={() => setIsProductDetailOpen(false)}
        product={selectedProduct}
      />

      <AddProductModal
        isOpen={isAddProductOpen}
        onClose={() => setIsAddProductOpen(false)}
        onAddProduct={handleAddProduct}
      />
    </div>
  );
};

export default ProductsTab;
